import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import * as request from 'supertest';
import { AppModule } from '../../app.module';
import { UsersService } from '../../modules/users/users.service';
import { WebsitesService } from '../../modules/websites/websites.service';
import { BlogsService } from '../../modules/blogs/blogs.service';
import { AuthService } from '../../modules/auth/auth.service';
import { UserRole } from '../../common/decorators/roles.decorator';
import { BlogStatus } from '../../modules/blogs/schemas/blog.schema';
import { WebsiteTheme } from '../../modules/websites/schemas/website.schema';

describe('Blog-Website Integration Tests', () => {
  let app: INestApplication;
  let mongod: MongoMemoryServer;
  let authService: AuthService;
  let usersService: UsersService;
  let websitesService: WebsitesService;
  let blogsService: BlogsService;

  // Test data
  let adminUser: any;
  let editorUser: any;
  let adminToken: string;
  let editorToken: string;
  let testWebsite: any;
  let testBlog: any;

  beforeAll(async () => {
    // Start in-memory MongoDB
    mongod = await MongoMemoryServer.create();
    const uri = mongod.getUri();

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        MongooseModule.forRoot(uri),
        AppModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Get services
    authService = moduleFixture.get<AuthService>(AuthService);
    usersService = moduleFixture.get<UsersService>(UsersService);
    websitesService = moduleFixture.get<WebsitesService>(WebsitesService);
    blogsService = moduleFixture.get<BlogsService>(BlogsService);

    // Create test users
    adminUser = await usersService.create({
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Admin',
      lastName: 'User',
      role: UserRole.ADMIN,
    });

    editorUser = await usersService.create({
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Editor',
      lastName: 'User',
      role: UserRole.EDITOR,
    });

    // Generate tokens using login
    const adminLoginResponse = await request(app.getHttpServer())
      .post('/api/v1/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' })
      .expect(200);
    
    const editorLoginResponse = await request(app.getHttpServer())
      .post('/api/v1/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' })
      .expect(200);

    adminToken = adminLoginResponse.body.token;
    editorToken = editorLoginResponse.body.token;
  });

  afterAll(async () => {
    await app.close();
    await mongod.stop();
  });

  describe('1. Website Creation and Management', () => {
    it('should create a website successfully', async () => {
      const websiteData = {
        name: 'Test Blog Website',
        domain: 'testblog.com',
        description: 'A test blog website',
        theme: WebsiteTheme.MINIMAL,
        seoSettings: {
          defaultTitle: 'Test Blog',
          defaultDescription: 'A test blog for integration testing',
        },
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/websites')
        .set('Authorization', `Bearer ${editorToken}`)
        .send(websiteData)
        .expect(201);

      expect(response.body.website).toBeDefined();
      expect(response.body.website.name).toBe(websiteData.name);
      expect(response.body.website.domain).toBe(websiteData.domain.toLowerCase());
      expect(response.body.website.apiKey).toBeDefined();
      expect(response.body.website.apiKey).toMatch(/^creasoft_[a-f0-9]{32}$/);
      expect(response.body.website.ownerId).toBe(editorUser._id.toString());
      expect(response.body.website.isActive).toBe(true);
      expect(response.body.website.blogCount).toBe(0);

      testWebsite = response.body.website;
    });

    it('should prevent duplicate domain creation', async () => {
      const duplicateWebsiteData = {
        name: 'Another Test Website',
        domain: 'testblog.com', // Same domain
        description: 'Another test website',
      };

      await request(app.getHttpServer())
        .post('/api/v1/websites')
        .set('Authorization', `Bearer ${editorToken}`)
        .send(duplicateWebsiteData)
        .expect(409);
    });

    it('should fetch user websites correctly', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/websites/my-websites')
        .set('Authorization', `Bearer ${editorToken}`)
        .expect(200);

      expect(response.body.websites).toBeDefined();
      expect(Array.isArray(response.body.websites)).toBe(true);
      expect(response.body.websites.length).toBe(1);
      expect(response.body.websites[0]._id).toBe(testWebsite._id);
      expect(response.body.websites[0].name).toBe(testWebsite.name);
    });

    it('should regenerate API key successfully', async () => {
      const oldApiKey = testWebsite.apiKey;

      const response = await request(app.getHttpServer())
        .post(`/api/v1/websites/${testWebsite._id}/regenerate-api-key`)
        .set('Authorization', `Bearer ${editorToken}`)
        .expect(200);

      expect(response.body.website.apiKey).toBeDefined();
      expect(response.body.website.apiKey).not.toBe(oldApiKey);
      expect(response.body.website.apiKey).toMatch(/^creasoft_[a-f0-9]{32}$/);

      // Update test website with new API key
      testWebsite.apiKey = response.body.website.apiKey;
    });
  });

  describe('2. Blog Creation and Website Assignment', () => {
    it('should create a blog and assign it to a website', async () => {
      const blogData = {
        title: 'My First Test Blog Post',
        slug: 'my-first-test-blog-post',
        content: '<p>This is the content of my first test blog post. It contains some HTML and should be long enough to calculate reading time.</p><p>Here is another paragraph to make it longer and more realistic.</p>',
        excerpt: 'This is a brief excerpt of my first test blog post.',
        websiteId: testWebsite._id,
        status: BlogStatus.DRAFT,
        categories: ['Technology', 'Testing'],
        tags: ['nodejs', 'testing', 'integration'],
        isFeatured: true,
        allowComments: true,
        seo: {
          seoTitle: 'My First Test Blog Post - SEO Title',
          seoDescription: 'This is the SEO description for my first test blog post.',
          focusKeyword: 'test blog',
          noIndex: false,
          noFollow: false,
        },
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/blogs')
        .set('Authorization', `Bearer ${editorToken}`)
        .send(blogData)
        .expect(201);

      expect(response.body.blog).toBeDefined();
      expect(response.body.blog.title).toBe(blogData.title);
      expect(response.body.blog.slug).toBe(blogData.slug);
      expect(response.body.blog.websiteId).toBe(testWebsite._id);
      expect(response.body.blog.authorId).toBe(editorUser._id.toString());
      expect(response.body.blog.status).toBe(BlogStatus.DRAFT);
      expect(response.body.blog.categories).toEqual(blogData.categories);
      expect(response.body.blog.tags).toEqual(blogData.tags);
      expect(response.body.blog.readingTime).toBeGreaterThan(0);
      expect(response.body.blog.viewCount).toBe(0);
      expect(response.body.blog.shareCount).toBe(0);
      expect(response.body.blog.isFeatured).toBe(true);
      expect(response.body.blog.seo.seoTitle).toBe(blogData.seo.seoTitle);

      testBlog = response.body.blog;
    });

    it('should increment website blog count after blog creation', async () => {
      const response = await request(app.getHttpServer())
        .get(`/api/v1/websites/${testWebsite._id}`)
        .set('Authorization', `Bearer ${editorToken}`)
        .expect(200);

      expect(response.body.website.blogCount).toBe(1);
    });

    it('should prevent duplicate slug within same website', async () => {
      const duplicateBlogData = {
        title: 'Another Blog Post',
        slug: 'my-first-test-blog-post', // Same slug
        content: '<p>Different content</p>',
        excerpt: 'Different excerpt',
        websiteId: testWebsite._id,
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/blogs')
        .set('Authorization', `Bearer ${editorToken}`)
        .send(duplicateBlogData)
        .expect(201);

      // Should auto-generate unique slug
      expect(response.body.blog.slug).not.toBe(duplicateBlogData.slug);
      expect(response.body.blog.slug).toMatch(/^my-first-test-blog-post-\d+$/);
    });

    it('should allow same slug in different websites', async () => {
      // Create another website
      const anotherWebsiteData = {
        name: 'Another Test Website',
        domain: 'anothertestblog.com',
      };

      const websiteResponse = await request(app.getHttpServer())
        .post('/api/v1/websites')
        .set('Authorization', `Bearer ${editorToken}`)
        .send(anotherWebsiteData)
        .expect(201);

      const anotherWebsite = websiteResponse.body.website;

      // Create blog with same slug in different website
      const blogData = {
        title: 'Same Slug Different Website',
        slug: 'my-first-test-blog-post', // Same slug as first blog
        content: '<p>Content for different website</p>',
        excerpt: 'Excerpt for different website',
        websiteId: anotherWebsite._id,
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/blogs')
        .set('Authorization', `Bearer ${editorToken}`)
        .send(blogData)
        .expect(201);

      expect(response.body.blog.slug).toBe(blogData.slug);
      expect(response.body.blog.websiteId).toBe(anotherWebsite._id);
    });

    it('should fetch blogs filtered by website', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/blogs')
        .query({ websiteId: testWebsite._id })
        .set('Authorization', `Bearer ${editorToken}`)
        .expect(200);

      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      
      // All blogs should belong to the specified website
      response.body.data.forEach((blog: any) => {
        expect(blog.websiteId._id || blog.websiteId).toBe(testWebsite._id);
      });
    });

    it('should publish a blog successfully', async () => {
      const response = await request(app.getHttpServer())
        .post(`/api/v1/blogs/${testBlog._id}/publish`)
        .set('Authorization', `Bearer ${editorToken}`)
        .expect(200);

      expect(response.body.blog.status).toBe(BlogStatus.PUBLISHED);
      expect(response.body.blog.publishedAt).toBeDefined();
      expect(new Date(response.body.blog.publishedAt)).toBeInstanceOf(Date);

      // Update test blog status
      testBlog.status = BlogStatus.PUBLISHED;
      testBlog.publishedAt = response.body.blog.publishedAt;
    });
  });

  describe('3. Public API Integration', () => {
    it('should authenticate with valid API key', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/public/website')
        .set('X-API-Key', testWebsite.apiKey)
        .expect(200);

      expect(response.body.website).toBeDefined();
      expect(response.body.website.name).toBe(testWebsite.name);
      expect(response.body.website.domain).toBe(testWebsite.domain);
      expect(response.body.website.blogCount).toBeGreaterThan(0);
    });

    it('should reject invalid API key', async () => {
      await request(app.getHttpServer())
        .get('/api/v1/public/website')
        .set('X-API-Key', 'invalid-api-key')
        .expect(401);
    });

    it('should reject missing API key', async () => {
      await request(app.getHttpServer())
        .get('/api/v1/public/website')
        .expect(401);
    });

    it('should fetch published blogs through public API', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .set('X-API-Key', testWebsite.apiKey)
        .expect(200);

      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.total).toBeGreaterThan(0);
      expect(response.body.page).toBe(1);
      expect(response.body.limit).toBe(10);

      // Should only return published blogs
      response.body.data.forEach((blog: any) => {
        expect(blog.id).toBeDefined();
        expect(blog.title).toBeDefined();
        expect(blog.slug).toBeDefined();
        expect(blog.excerpt).toBeDefined();
        expect(blog.publishedAt).toBeDefined();
        expect(blog.author).toBeDefined();
        expect(blog.seo).toBeDefined();
        // Should not include sensitive fields
        expect(blog._id).toBeUndefined();
        expect(blog.websiteId).toBeUndefined();
        expect(blog.authorId).toBeUndefined();
      });
    });

    it('should fetch blog by slug through public API', async () => {
      const response = await request(app.getHttpServer())
        .get(`/api/v1/public/blogs/${testBlog.slug}`)
        .set('X-API-Key', testWebsite.apiKey)
        .expect(200);

      expect(response.body.blog).toBeDefined();
      expect(response.body.blog.id).toBeDefined();
      expect(response.body.blog.title).toBe(testBlog.title);
      expect(response.body.blog.slug).toBe(testBlog.slug);
      expect(response.body.blog.content).toBeDefined();
      expect(response.body.blog.author).toBeDefined();
      expect(response.body.blog.website).toBeDefined();
      expect(response.body.blog.website.name).toBe(testWebsite.name);
      expect(response.body.blog.viewCount).toBeGreaterThan(0); // Should increment view count
    });

    it('should return 404 for non-existent blog slug', async () => {
      await request(app.getHttpServer())
        .get('/api/v1/public/blogs/non-existent-slug')
        .set('X-API-Key', testWebsite.apiKey)
        .expect(404);
    });

    it('should not return draft blogs through public API', async () => {
      // Create a draft blog
      const draftBlogData = {
        title: 'Draft Blog Post',
        slug: 'draft-blog-post',
        content: '<p>This is a draft blog post</p>',
        excerpt: 'This is a draft excerpt',
        websiteId: testWebsite._id,
        status: BlogStatus.DRAFT,
      };

      const createResponse = await request(app.getHttpServer())
        .post('/api/v1/blogs')
        .set('Authorization', `Bearer ${editorToken}`)
        .send(draftBlogData)
        .expect(201);

      const draftBlog = createResponse.body.blog;

      // Try to fetch draft blog through public API
      await request(app.getHttpServer())
        .get(`/api/v1/public/blogs/${draftBlog.slug}`)
        .set('X-API-Key', testWebsite.apiKey)
        .expect(404);

      // Draft blog should not appear in public blogs list
      const listResponse = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .set('X-API-Key', testWebsite.apiKey)
        .expect(200);

      const draftBlogInList = listResponse.body.data.find(
        (blog: any) => blog.slug === draftBlog.slug
      );
      expect(draftBlogInList).toBeUndefined();
    });

    it('should filter blogs by category through public API', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .query({ category: 'Technology' })
        .set('X-API-Key', testWebsite.apiKey)
        .expect(200);

      expect(response.body.data).toBeDefined();
      response.body.data.forEach((blog: any) => {
        expect(blog.categories).toContain('Technology');
      });
    });

    it('should filter blogs by tags through public API', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .query({ tags: 'nodejs,testing' })
        .set('X-API-Key', testWebsite.apiKey)
        .expect(200);

      expect(response.body.data).toBeDefined();
      response.body.data.forEach((blog: any) => {
        const hasNodejs = blog.tags.includes('nodejs');
        const hasTesting = blog.tags.includes('testing');
        expect(hasNodejs || hasTesting).toBe(true);
      });
    });

    it('should search blogs through public API', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .query({ search: 'test' })
        .set('X-API-Key', testWebsite.apiKey)
        .expect(200);

      expect(response.body.data).toBeDefined();
      response.body.data.forEach((blog: any) => {
        const searchTerm = 'test';
        const titleMatch = blog.title.toLowerCase().includes(searchTerm);
        const excerptMatch = blog.excerpt.toLowerCase().includes(searchTerm);
        expect(titleMatch || excerptMatch).toBe(true);
      });
    });

    it('should get categories through public API', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/public/categories')
        .set('X-API-Key', testWebsite.apiKey)
        .expect(200);

      expect(response.body.categories).toBeDefined();
      expect(Array.isArray(response.body.categories)).toBe(true);
      expect(response.body.categories).toContain('Technology');
      expect(response.body.categories).toContain('Testing');
    });

    it('should get tags through public API', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/public/tags')
        .set('X-API-Key', testWebsite.apiKey)
        .expect(200);

      expect(response.body.tags).toBeDefined();
      expect(Array.isArray(response.body.tags)).toBe(true);
      expect(response.body.tags).toContain('nodejs');
      expect(response.body.tags).toContain('testing');
      expect(response.body.tags).toContain('integration');
    });

    it('should increment share count through public API', async () => {
      const response = await request(app.getHttpServer())
        .post(`/api/v1/public/blogs/${testBlog._id}/share`)
        .set('X-API-Key', testWebsite.apiKey)
        .expect(200);

      expect(response.body.message).toBe('Share count incremented successfully');

      // Verify share count was incremented
      const blogResponse = await request(app.getHttpServer())
        .get(`/api/v1/blogs/${testBlog._id}`)
        .set('Authorization', `Bearer ${editorToken}`)
        .expect(200);

      expect(blogResponse.body.blog.shareCount).toBeGreaterThan(0);
    });
  });

  describe('4. Permission and Security Tests', () => {
    it('should prevent access to other users websites', async () => {
      // Create another user
      const anotherUser = await usersService.create({
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Another',
        lastName: 'User',
        role: UserRole.EDITOR,
      });

      const anotherLoginResponse = await request(app.getHttpServer())
        .post('/api/v1/auth/login')
        .send({ email: '<EMAIL>', password: 'password123' })
        .expect(200);
      
      const anotherToken = anotherLoginResponse.body.token;

      // Try to access the test website
      await request(app.getHttpServer())
        .get(`/api/v1/websites/${testWebsite._id}`)
        .set('Authorization', `Bearer ${anotherToken}`)
        .expect(403);
    });

    it('should prevent creating blogs for other users websites', async () => {
      const anotherUser = await usersService.create({
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Another2',
        lastName: 'User',
        role: UserRole.EDITOR,
      });

      const anotherLoginResponse = await request(app.getHttpServer())
        .post('/api/v1/auth/login')
        .send({ email: '<EMAIL>', password: 'password123' })
        .expect(200);
      
      const anotherToken = anotherLoginResponse.body.token;

      const blogData = {
        title: 'Unauthorized Blog',
        slug: 'unauthorized-blog',
        content: '<p>This should not be allowed</p>',
        excerpt: 'Unauthorized excerpt',
        websiteId: testWebsite._id, // Trying to use another user's website
      };

      await request(app.getHttpServer())
        .post('/api/v1/blogs')
        .set('Authorization', `Bearer ${anotherToken}`)
        .send(blogData)
        .expect(403);
    });

    it('should allow admin to access all websites and blogs', async () => {
      // Admin should be able to access any website
      const websiteResponse = await request(app.getHttpServer())
        .get(`/api/v1/websites/${testWebsite._id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(websiteResponse.body.website._id).toBe(testWebsite._id);

      // Admin should be able to access any blog
      const blogResponse = await request(app.getHttpServer())
        .get(`/api/v1/blogs/${testBlog._id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(blogResponse.body.blog._id).toBe(testBlog._id);
    });

    it('should validate API key belongs to correct website', async () => {
      // Create another website with different API key
      const anotherWebsiteData = {
        name: 'Another Website',
        domain: 'another-website.com',
      };

      const websiteResponse = await request(app.getHttpServer())
        .post('/api/v1/websites')
        .set('Authorization', `Bearer ${editorToken}`)
        .send(anotherWebsiteData)
        .expect(201);

      const anotherWebsite = websiteResponse.body.website;

      // Try to access first website's blogs with second website's API key
      const response = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .set('X-API-Key', anotherWebsite.apiKey)
        .expect(200);

      // Should only return blogs from the second website (none in this case)
      expect(response.body.data.length).toBe(0);
    });
  });

  describe('5. Data Consistency Tests', () => {
    it('should maintain blog count consistency when deleting blogs', async () => {
      // Get current blog count
      const websiteResponse = await request(app.getHttpServer())
        .get(`/api/v1/websites/${testWebsite._id}`)
        .set('Authorization', `Bearer ${editorToken}`)
        .expect(200);

      const initialBlogCount = websiteResponse.body.website.blogCount;

      // Create a new blog
      const blogData = {
        title: 'Blog to Delete',
        slug: 'blog-to-delete',
        content: '<p>This blog will be deleted</p>',
        excerpt: 'This blog will be deleted',
        websiteId: testWebsite._id,
      };

      const createResponse = await request(app.getHttpServer())
        .post('/api/v1/blogs')
        .set('Authorization', `Bearer ${editorToken}`)
        .send(blogData)
        .expect(201);

      const blogToDelete = createResponse.body.blog;

      // Verify blog count increased
      const afterCreateResponse = await request(app.getHttpServer())
        .get(`/api/v1/websites/${testWebsite._id}`)
        .set('Authorization', `Bearer ${editorToken}`)
        .expect(200);

      expect(afterCreateResponse.body.website.blogCount).toBe(initialBlogCount + 1);

      // Delete the blog
      await request(app.getHttpServer())
        .delete(`/api/v1/blogs/${blogToDelete._id}`)
        .set('Authorization', `Bearer ${editorToken}`)
        .expect(200);

      // Verify blog count decreased
      const afterDeleteResponse = await request(app.getHttpServer())
        .get(`/api/v1/websites/${testWebsite._id}`)
        .set('Authorization', `Bearer ${editorToken}`)
        .expect(200);

      expect(afterDeleteResponse.body.website.blogCount).toBe(initialBlogCount);
    });

    it('should handle slug generation correctly', async () => {
      const blogData = {
        title: 'Test Blog with Special Characters & Symbols!',
        content: '<p>Content</p>',
        excerpt: 'Excerpt',
        websiteId: testWebsite._id,
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/blogs')
        .set('Authorization', `Bearer ${editorToken}`)
        .send(blogData)
        .expect(201);

      expect(response.body.blog.slug).toBe('test-blog-with-special-characters-symbols');
    });

    it('should calculate reading time correctly', async () => {
      const longContent = '<p>' + 'word '.repeat(300) + '</p>'; // ~300 words
      
      const blogData = {
        title: 'Long Blog Post',
        slug: 'long-blog-post',
        content: longContent,
        excerpt: 'A long blog post for testing reading time calculation',
        websiteId: testWebsite._id,
      };

      const response = await request(app.getHttpServer())
        .post('/api/v1/blogs')
        .set('Authorization', `Bearer ${editorToken}`)
        .send(blogData)
        .expect(201);

      // Should be approximately 1-2 minutes for 300 words (assuming 200 WPM)
      expect(response.body.blog.readingTime).toBeGreaterThan(0);
      expect(response.body.blog.readingTime).toBeLessThan(5);
    });
  });

  describe('6. Error Handling Tests', () => {
    it('should handle invalid website ID in blog creation', async () => {
      const blogData = {
        title: 'Invalid Website Blog',
        slug: 'invalid-website-blog',
        content: '<p>Content</p>',
        excerpt: 'Excerpt',
        websiteId: '507f1f77bcf86cd799439011', // Valid ObjectId but non-existent
      };

      await request(app.getHttpServer())
        .post('/api/v1/blogs')
        .set('Authorization', `Bearer ${editorToken}`)
        .send(blogData)
        .expect(404);
    });

    it('should handle malformed ObjectId in requests', async () => {
      await request(app.getHttpServer())
        .get('/api/v1/blogs/invalid-object-id')
        .set('Authorization', `Bearer ${editorToken}`)
        .expect(400);
    });

    it('should validate required fields in blog creation', async () => {
      const incompleteBlogData = {
        title: 'Incomplete Blog',
        // Missing required fields: content, excerpt, websiteId
      };

      await request(app.getHttpServer())
        .post('/api/v1/blogs')
        .set('Authorization', `Bearer ${editorToken}`)
        .send(incompleteBlogData)
        .expect(400);
    });

    it('should handle inactive website API key', async () => {
      // Deactivate the website
      await request(app.getHttpServer())
        .patch(`/api/v1/websites/${testWebsite._id}`)
        .set('Authorization', `Bearer ${editorToken}`)
        .send({ isActive: false })
        .expect(200);

      // Try to use the API key
      await request(app.getHttpServer())
        .get('/api/v1/public/website')
        .set('X-API-Key', testWebsite.apiKey)
        .expect(401);

      // Reactivate the website for other tests
      await request(app.getHttpServer())
        .patch(`/api/v1/websites/${testWebsite._id}`)
        .set('Authorization', `Bearer ${editorToken}`)
        .send({ isActive: true })
        .expect(200);
    });
  });

  describe('7. Performance and Pagination Tests', () => {
    beforeAll(async () => {
      // Create multiple blogs for pagination testing
      const blogPromises = [];
      for (let i = 1; i <= 15; i++) {
        const blogData = {
          title: `Test Blog ${i}`,
          slug: `test-blog-${i}`,
          content: `<p>Content for test blog ${i}</p>`,
          excerpt: `Excerpt for test blog ${i}`,
          websiteId: testWebsite._id,
          status: BlogStatus.PUBLISHED,
          categories: i % 2 === 0 ? ['Even'] : ['Odd'],
          tags: [`tag${i}`, 'common-tag'],
        };

        blogPromises.push(
          request(app.getHttpServer())
            .post('/api/v1/blogs')
            .set('Authorization', `Bearer ${editorToken}`)
            .send(blogData)
        );
      }

      await Promise.all(blogPromises);
    });

    it('should handle pagination correctly', async () => {
      const page1Response = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .query({ page: 1, limit: 5 })
        .set('X-API-Key', testWebsite.apiKey)
        .expect(200);

      expect(page1Response.body.data.length).toBe(5);
      expect(page1Response.body.page).toBe(1);
      expect(page1Response.body.limit).toBe(5);
      expect(page1Response.body.hasNext).toBe(true);
      expect(page1Response.body.hasPrev).toBe(false);

      const page2Response = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .query({ page: 2, limit: 5 })
        .set('X-API-Key', testWebsite.apiKey)
        .expect(200);

      expect(page2Response.body.data.length).toBe(5);
      expect(page2Response.body.page).toBe(2);
      expect(page2Response.body.hasNext).toBe(true);
      expect(page2Response.body.hasPrev).toBe(true);

      // Ensure different blogs on different pages
      const page1Ids = page1Response.body.data.map((blog: any) => blog.id);
      const page2Ids = page2Response.body.data.map((blog: any) => blog.id);
      const intersection = page1Ids.filter((id: string) => page2Ids.includes(id));
      expect(intersection.length).toBe(0);
    });

    it('should handle sorting correctly', async () => {
      const ascResponse = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .query({ sortBy: 'title', sortOrder: 'asc', limit: 5 })
        .set('X-API-Key', testWebsite.apiKey)
        .expect(200);

      const descResponse = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .query({ sortBy: 'title', sortOrder: 'desc', limit: 5 })
        .set('X-API-Key', testWebsite.apiKey)
        .expect(200);

      // First blog in asc should be different from first blog in desc
      expect(ascResponse.body.data[0].id).not.toBe(descResponse.body.data[0].id);

      // Verify ascending order
      for (let i = 1; i < ascResponse.body.data.length; i++) {
        expect(ascResponse.body.data[i].title >= ascResponse.body.data[i - 1].title).toBe(true);
      }
    });

    it('should handle large result sets efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .query({ limit: 50 })
        .set('X-API-Key', testWebsite.apiKey)
        .expect(200);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.body.data).toBeDefined();
      expect(responseTime).toBeLessThan(1000); // Should respond within 1 second
    });
  });

  describe('8. Complete Integration Flow Test', () => {
    let testUser: any;
    let testUserToken: string;
    let testDomain: any;
    let testBlogData: any;
    let createdBlog: any;

    it('should complete the full workflow: user registration → domain creation → blog creation → public API access', async () => {
      // Step 1: User Registration
      console.log('🔄 Step 1: User Registration');
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Integration',
        lastName: 'Test',
      };

      const registerResponse = await request(app.getHttpServer())
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(201);

      testUser = registerResponse.body.user;
      testUserToken = registerResponse.body.token;

      expect(testUser).toBeDefined();
      expect(testUser.email).toBe(userData.email);
      expect(testUserToken).toBeDefined();
      console.log('✅ User registered successfully:', testUser.email);

      // Step 2: Domain/Website Creation
      console.log('🔄 Step 2: Domain/Website Creation');
      const domainData = {
        name: 'Integration Test Website',
        domain: 'integration-test.com',
        description: 'A test website for integration testing',
        theme: WebsiteTheme.MINIMAL,
      };

      const domainResponse = await request(app.getHttpServer())
        .post('/api/v1/websites')
        .set('Authorization', `Bearer ${testUserToken}`)
        .send(domainData)
        .expect(201);

      testDomain = domainResponse.body.website;

      expect(testDomain).toBeDefined();
      expect(testDomain.name).toBe(domainData.name);
      expect(testDomain.domain).toBe(domainData.domain);
      expect(testDomain.apiKey).toBeDefined();
      expect(testDomain.apiKey).toMatch(/^creasoft_[a-f0-9]{32}$/);
      expect(testDomain.ownerId).toBe(testUser._id);
      expect(testDomain.isActive).toBe(true);
      expect(testDomain.blogCount).toBe(0);
      console.log('✅ Domain created successfully:', testDomain.domain);
      console.log('🔑 API Key generated:', testDomain.apiKey);

      // Step 3: Blog Creation
      console.log('🔄 Step 3: Blog Creation');
      testBlogData = {
        title: 'Integration Test Blog Post',
        slug: 'integration-test-blog-post',
        content: '<h1>Welcome to Integration Testing</h1><p>This is a comprehensive test blog post created during integration testing. It contains <strong>rich content</strong> with various HTML elements to test the complete flow.</p><p>This blog post will be used to test the public API endpoints and ensure everything works correctly from creation to public consumption.</p>',
        excerpt: 'A comprehensive test blog post for integration testing with rich content and public API validation.',
        websiteId: testDomain._id,
        categories: ['Integration', 'Testing', 'API'],
        tags: ['integration', 'test', 'api', 'blog'],
        isFeatured: true,
        allowComments: true,
        seo: {
          metaTitle: 'Integration Test Blog - Complete API Testing',
          metaDescription: 'A comprehensive integration test blog post to validate the complete blog creation and public API flow.',
          keywords: ['integration', 'testing', 'api', 'blog', 'cms'],
        },
      };

      const blogResponse = await request(app.getHttpServer())
        .post('/api/v1/blogs')
        .set('Authorization', `Bearer ${testUserToken}`)
        .send(testBlogData)
        .expect(201);

      createdBlog = blogResponse.body.blog;

      expect(createdBlog).toBeDefined();
      expect(createdBlog.title).toBe(testBlogData.title);
      expect(createdBlog.slug).toBe(testBlogData.slug);
      expect(createdBlog.content).toBe(testBlogData.content);
      expect(createdBlog.excerpt).toBe(testBlogData.excerpt);
      expect(createdBlog.websiteId).toBe(testDomain._id);
      expect(createdBlog.authorId).toBe(testUser._id);
      expect(createdBlog.status).toBe(BlogStatus.DRAFT);
      expect(createdBlog.categories).toEqual(testBlogData.categories);
      expect(createdBlog.tags).toEqual(testBlogData.tags);
      expect(createdBlog.isFeatured).toBe(true);
      expect(createdBlog.allowComments).toBe(true);
      expect(createdBlog.readingTime).toBeGreaterThan(0);
      expect(createdBlog.viewCount).toBe(0);
      expect(createdBlog.shareCount).toBe(0);
      expect(createdBlog.seo).toBeDefined();
      expect(createdBlog.seo.metaTitle).toBe(testBlogData.seo.metaTitle);
      console.log('✅ Blog created successfully:', createdBlog.title);
      console.log('📊 Reading time calculated:', createdBlog.readingTime, 'minutes');

      // Step 4: Blog Publishing
      console.log('🔄 Step 4: Blog Publishing');
      const publishResponse = await request(app.getHttpServer())
        .post(`/api/v1/blogs/${createdBlog._id}/publish`)
        .set('Authorization', `Bearer ${testUserToken}`)
        .expect(200);

      const publishedBlog = publishResponse.body.blog;

      expect(publishedBlog.status).toBe(BlogStatus.PUBLISHED);
      expect(publishedBlog.publishedAt).toBeDefined();
      expect(new Date(publishedBlog.publishedAt)).toBeInstanceOf(Date);
      console.log('✅ Blog published successfully at:', publishedBlog.publishedAt);

      // Verify website blog count updated
      const updatedDomainResponse = await request(app.getHttpServer())
        .get(`/api/v1/websites/${testDomain._id}`)
        .set('Authorization', `Bearer ${testUserToken}`)
        .expect(200);

      expect(updatedDomainResponse.body.website.blogCount).toBe(1);
      console.log('✅ Website blog count updated:', updatedDomainResponse.body.website.blogCount);
    });

    it('should validate public API access with the created domain and blog', async () => {
      // Step 5: Public API - Website Information
      console.log('🔄 Step 5: Public API - Website Information');
      const websiteInfoResponse = await request(app.getHttpServer())
        .get('/api/v1/public/website')
        .set('X-API-Key', testDomain.apiKey)
        .expect(200);

      const websiteInfo = websiteInfoResponse.body.website;
      expect(websiteInfo).toBeDefined();
      expect(websiteInfo.name).toBe(testDomain.name);
      expect(websiteInfo.domain).toBe(testDomain.domain);
      expect(websiteInfo.description).toBe(testDomain.description);
      expect(websiteInfo.theme).toBe(testDomain.theme);
      expect(websiteInfo.blogCount).toBe(1);
      expect(websiteInfo.isActive).toBe(true);
      console.log('✅ Website info retrieved via public API:', websiteInfo.name);

      // Step 6: Public API - Fetch All Blogs
      console.log('🔄 Step 6: Public API - Fetch All Blogs');
      const allBlogsResponse = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .set('X-API-Key', testDomain.apiKey)
        .expect(200);

      expect(allBlogsResponse.body.data).toBeDefined();
      expect(allBlogsResponse.body.data.length).toBe(1);
      expect(allBlogsResponse.body.total).toBe(1);
      expect(allBlogsResponse.body.page).toBe(1);
      expect(allBlogsResponse.body.limit).toBe(10);
      expect(allBlogsResponse.body.totalPages).toBe(1);
      expect(allBlogsResponse.body.hasNext).toBe(false);
      expect(allBlogsResponse.body.hasPrev).toBe(false);

      const blogFromList = allBlogsResponse.body.data[0];
      expect(blogFromList.id).toBeDefined();
      expect(blogFromList.title).toBe(testBlogData.title);
      expect(blogFromList.slug).toBe(testBlogData.slug);
      expect(blogFromList.excerpt).toBe(testBlogData.excerpt);
      expect(blogFromList.featuredImage).toBeUndefined(); // No image uploaded
      expect(blogFromList.publishedAt).toBeDefined();
      expect(blogFromList.categories).toEqual(testBlogData.categories);
      expect(blogFromList.tags).toEqual(testBlogData.tags);
      expect(blogFromList.readingTime).toBeGreaterThan(0);
      expect(blogFromList.viewCount).toBe(0);
      expect(blogFromList.shareCount).toBe(0);
      expect(blogFromList.isFeatured).toBe(true);
      expect(blogFromList.author).toBeDefined();
      expect(blogFromList.author.firstName).toBe(testUser.firstName);
      expect(blogFromList.author.lastName).toBe(testUser.lastName);
      expect(blogFromList.seo).toBeDefined();
      console.log('✅ All blogs fetched via public API:', blogFromList.title);

      // Step 7: Public API - Fetch Single Blog by Slug
      console.log('🔄 Step 7: Public API - Fetch Single Blog by Slug');
      const singleBlogResponse = await request(app.getHttpServer())
        .get(`/api/v1/public/blogs/${testBlogData.slug}`)
        .set('X-API-Key', testDomain.apiKey)
        .expect(200);

      const singleBlog = singleBlogResponse.body.blog;
      expect(singleBlog).toBeDefined();
      expect(singleBlog.id).toBeDefined();
      expect(singleBlog.title).toBe(testBlogData.title);
      expect(singleBlog.slug).toBe(testBlogData.slug);
      expect(singleBlog.content).toBe(testBlogData.content);
      expect(singleBlog.excerpt).toBe(testBlogData.excerpt);
      expect(singleBlog.publishedAt).toBeDefined();
      expect(singleBlog.categories).toEqual(testBlogData.categories);
      expect(singleBlog.tags).toEqual(testBlogData.tags);
      expect(singleBlog.readingTime).toBeGreaterThan(0);
      expect(singleBlog.viewCount).toBe(1); // Should increment view count
      expect(singleBlog.shareCount).toBe(0);
      expect(singleBlog.isFeatured).toBe(true);
      expect(singleBlog.allowComments).toBe(true);
      expect(singleBlog.author).toBeDefined();
      expect(singleBlog.author.firstName).toBe(testUser.firstName);
      expect(singleBlog.author.lastName).toBe(testUser.lastName);
      expect(singleBlog.seo).toBeDefined();
      expect(singleBlog.seo.metaTitle).toBe(testBlogData.seo.metaTitle);
      expect(singleBlog.seo.metaDescription).toBe(testBlogData.seo.metaDescription);
      expect(singleBlog.website).toBeDefined();
      expect(singleBlog.website.name).toBe(testDomain.name);
      expect(singleBlog.website.domain).toBe(testDomain.domain);
      console.log('✅ Single blog fetched via public API:', singleBlog.title);
      console.log('👁️ View count incremented to:', singleBlog.viewCount);

      // Step 8: Public API - Test Filtering and Search
      console.log('🔄 Step 8: Public API - Test Filtering and Search');

      // Test category filtering
      const categoryFilterResponse = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .query({ category: 'Integration' })
        .set('X-API-Key', testDomain.apiKey)
        .expect(200);

      expect(categoryFilterResponse.body.data.length).toBe(1);
      expect(categoryFilterResponse.body.data[0].categories).toContain('Integration');
      console.log('✅ Category filtering works:', categoryFilterResponse.body.data[0].categories);

      // Test tag filtering
      const tagFilterResponse = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .query({ tags: 'integration,test' })
        .set('X-API-Key', testDomain.apiKey)
        .expect(200);

      expect(tagFilterResponse.body.data.length).toBe(1);
      expect(tagFilterResponse.body.data[0].tags).toContain('integration');
      expect(tagFilterResponse.body.data[0].tags).toContain('test');
      console.log('✅ Tag filtering works:', tagFilterResponse.body.data[0].tags);

      // Test search functionality
      const searchResponse = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .query({ search: 'Integration Test' })
        .set('X-API-Key', testDomain.apiKey)
        .expect(200);

      expect(searchResponse.body.data.length).toBe(1);
      expect(searchResponse.body.data[0].title).toContain('Integration Test');
      console.log('✅ Search functionality works:', searchResponse.body.data[0].title);

      // Test featured blogs filtering
      const featuredResponse = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .query({ featured: true })
        .set('X-API-Key', testDomain.apiKey)
        .expect(200);

      expect(featuredResponse.body.data.length).toBe(1);
      expect(featuredResponse.body.data[0].isFeatured).toBe(true);
      console.log('✅ Featured filtering works:', featuredResponse.body.data[0].isFeatured);

      // Step 9: Public API - Test Categories and Tags Endpoints
      console.log('🔄 Step 9: Public API - Categories and Tags');

      const categoriesResponse = await request(app.getHttpServer())
        .get('/api/v1/public/categories')
        .set('X-API-Key', testDomain.apiKey)
        .expect(200);

      expect(categoriesResponse.body.categories).toBeDefined();
      expect(categoriesResponse.body.categories).toEqual(expect.arrayContaining(['Integration', 'Testing', 'API']));
      console.log('✅ Categories endpoint works:', categoriesResponse.body.categories);

      const tagsResponse = await request(app.getHttpServer())
        .get('/api/v1/public/tags')
        .set('X-API-Key', testDomain.apiKey)
        .expect(200);

      expect(tagsResponse.body.tags).toBeDefined();
      expect(tagsResponse.body.tags).toEqual(expect.arrayContaining(['integration', 'test', 'api', 'blog']));
      console.log('✅ Tags endpoint works:', tagsResponse.body.tags);

      // Step 10: Public API - Test Share Count Increment
      console.log('🔄 Step 10: Public API - Share Count Increment');

      const shareResponse = await request(app.getHttpServer())
        .post(`/api/v1/public/blogs/${createdBlog._id}/share`)
        .set('X-API-Key', testDomain.apiKey)
        .expect(200);

      expect(shareResponse.body.message).toBe('Share count incremented successfully');

      // Verify share count was incremented
      const updatedBlogResponse = await request(app.getHttpServer())
        .get(`/api/v1/public/blogs/${testBlogData.slug}`)
        .set('X-API-Key', testDomain.apiKey)
        .expect(200);

      expect(updatedBlogResponse.body.blog.shareCount).toBe(1);
      expect(updatedBlogResponse.body.blog.viewCount).toBe(2); // Should increment again
      console.log('✅ Share count incremented to:', updatedBlogResponse.body.blog.shareCount);
      console.log('👁️ View count now:', updatedBlogResponse.body.blog.viewCount);
    });

    it('should validate API key security and isolation', async () => {
      // Step 11: Security Testing
      console.log('🔄 Step 11: API Key Security Testing');

      // Test with invalid API key
      await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .set('X-API-Key', 'invalid-api-key')
        .expect(401);
      console.log('✅ Invalid API key rejected');

      // Test without API key
      await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .expect(401);
      console.log('✅ Missing API key rejected');

      // Test with malformed API key
      await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .set('X-API-Key', 'malformed_key')
        .expect(401);
      console.log('✅ Malformed API key rejected');

      // Create another domain to test isolation
      const anotherDomainData = {
        name: 'Another Test Website',
        domain: 'another-test.com',
        description: 'Another test website for isolation testing',
        theme: WebsiteTheme.CORPORATE,
      };

      const anotherDomainResponse = await request(app.getHttpServer())
        .post('/api/v1/websites')
        .set('Authorization', `Bearer ${testUserToken}`)
        .send(anotherDomainData)
        .expect(201);

      const anotherDomain = anotherDomainResponse.body.website;

      // Test that another domain's API key doesn't return our blogs
      const isolationTestResponse = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .set('X-API-Key', anotherDomain.apiKey)
        .expect(200);

      expect(isolationTestResponse.body.data.length).toBe(0);
      console.log('✅ API key isolation works - no cross-domain data leakage');
    });

    it('should validate response format matches documentation', async () => {
      // Step 12: Response Format Validation
      console.log('🔄 Step 12: Response Format Validation');

      const blogsResponse = await request(app.getHttpServer())
        .get('/api/v1/public/blogs')
        .set('X-API-Key', testDomain.apiKey)
        .expect(200);

      // Validate response structure matches documentation
      expect(blogsResponse.body).toHaveProperty('data');
      expect(blogsResponse.body).toHaveProperty('total');
      expect(blogsResponse.body).toHaveProperty('page');
      expect(blogsResponse.body).toHaveProperty('limit');
      expect(blogsResponse.body).toHaveProperty('totalPages');
      expect(blogsResponse.body).toHaveProperty('hasNext');
      expect(blogsResponse.body).toHaveProperty('hasPrev');
      expect(blogsResponse.body).toHaveProperty('message');

      const blog = blogsResponse.body.data[0];
      expect(blog).toHaveProperty('id');
      expect(blog).toHaveProperty('title');
      expect(blog).toHaveProperty('slug');
      expect(blog).toHaveProperty('excerpt');
      expect(blog).toHaveProperty('publishedAt');
      expect(blog).toHaveProperty('readingTime');
      expect(blog).toHaveProperty('categories');
      expect(blog).toHaveProperty('tags');
      expect(blog).toHaveProperty('author');
      expect(blog.author).toHaveProperty('firstName');
      expect(blog.author).toHaveProperty('lastName');
      console.log('✅ Response format matches documentation');

      // Test single blog response format
      const singleBlogResponse = await request(app.getHttpServer())
        .get(`/api/v1/public/blogs/${testBlogData.slug}`)
        .set('X-API-Key', testDomain.apiKey)
        .expect(200);

      expect(singleBlogResponse.body).toHaveProperty('blog');
      expect(singleBlogResponse.body).toHaveProperty('message');

      const singleBlog = singleBlogResponse.body.blog;
      expect(singleBlog).toHaveProperty('content'); // Full content should be included
      expect(singleBlog).toHaveProperty('website');
      expect(singleBlog.website).toHaveProperty('name');
      expect(singleBlog.website).toHaveProperty('domain');
      console.log('✅ Single blog response format matches documentation');
    });

    afterAll(async () => {
      console.log('🧹 Cleaning up integration test data...');

      // Clean up test data
      if (createdBlog) {
        await request(app.getHttpServer())
          .delete(`/api/v1/blogs/${createdBlog._id}`)
          .set('Authorization', `Bearer ${testUserToken}`)
          .catch(() => {}); // Ignore errors during cleanup
      }

      if (testDomain) {
        await request(app.getHttpServer())
          .delete(`/api/v1/websites/${testDomain._id}`)
          .set('Authorization', `Bearer ${testUserToken}`)
          .catch(() => {}); // Ignore errors during cleanup
      }

      console.log('✅ Integration test cleanup completed');
    });
  });
});