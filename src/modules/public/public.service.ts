import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Blog, BlogDocument, BlogStatus } from '../blogs/schemas/blog.schema';
import { WebsiteDocument } from '../websites/schemas/website.schema';
import { PublicBlogQueryDto } from './dto/public-query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination.dto';

@Injectable()
export class PublicService {
  constructor(
    @InjectModel(Blog.name) private blogModel: Model<BlogDocument>,
  ) {}

  async getBlogs(
    website: WebsiteDocument,
    query: PublicBlogQueryDto,
  ): Promise<PaginationResponseDto<any>> {
    const { 
      page = 1, 
      limit = 10, 
      search, 
      category, 
      tags, 
      sortBy = 'publishedAt', 
      sortOrder = 'desc',
      featured = false 
    } = query;
    
    const filter: any = {
      websiteId: website._id.toString(),
      status: BlogStatus.PUBLISHED,
      publishedAt: { $lte: new Date() },
    };
    
    if (featured) {
      filter.isFeatured = true;
    }
    
    if (category) {
      filter.categories = { $in: [category] };
    }
    
    if (tags) {
      const tagArray = tags.split(',').map(tag => tag.trim());
      filter.tags = { $in: tagArray };
    }
    
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { excerpt: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } },
      ];
    }

    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [blogs, total] = await Promise.all([
      this.blogModel
        .find(filter)
        .populate('authorId', 'firstName lastName')
        .select([
          'title',
          'slug',
          'content',
          'excerpt',
          'featuredImage',
          'publishedAt',
          'categories',
          'tags',
          'readingTime',
          'viewCount',
          'shareCount',
          'isFeatured',
          'seo',
          'authorId',
        ])
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean()
        .exec(),
      this.blogModel.countDocuments(filter),
    ]);

    const totalPages = Math.ceil(total / limit);

    // Transform blogs for public API
    const transformedBlogs = blogs.map(blog => ({
      id: blog._id,
      title: blog.title,
      slug: blog.slug,
      content: blog.content,
      excerpt: blog.excerpt,
      featuredImage: blog.featuredImage,
      publishedAt: blog.publishedAt,
      categories: blog.categories,
      tags: blog.tags,
      readingTime: blog.readingTime,
      viewCount: blog.viewCount,
      shareCount: blog.shareCount,
      isFeatured: blog.isFeatured,
      author: blog.authorId ? {
        firstName: (blog.authorId as any).firstName,
        lastName: (blog.authorId as any).lastName,
        name: `${(blog.authorId as any).firstName} ${(blog.authorId as any).lastName}`,
      } : null,
      seo: {
        title: blog.seo?.seoTitle || blog.title,
        description: blog.seo?.seoDescription || blog.excerpt,
        ogImage: blog.seo?.ogImage || blog.featuredImage,
        canonicalUrl: blog.seo?.canonicalUrl,
        noIndex: blog.seo?.noIndex || false,
        noFollow: blog.seo?.noFollow || false,
      },
    }));

    return {
      data: transformedBlogs,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async getBlogBySlug(
    website: WebsiteDocument,
    slug: string,
  ): Promise<any> {
    const blog = await this.blogModel
      .findOne({
        websiteId: website._id.toString(),
        slug,
        status: BlogStatus.PUBLISHED,
        publishedAt: { $lte: new Date() },
      })
      .populate('authorId', 'firstName lastName')
      .lean()
      .exec();

    if (!blog) {
      return null;
    }

    // Increment view count (fire and forget)
    this.blogModel.findByIdAndUpdate(blog._id, { $inc: { viewCount: 1 } }).exec();

    // Transform blog for public API
    return {
      id: blog._id,
      title: blog.title,
      slug: blog.slug,
      content: blog.content,
      excerpt: blog.excerpt,
      featuredImage: blog.featuredImage,
      publishedAt: blog.publishedAt,
      categories: blog.categories,
      tags: blog.tags,
      readingTime: blog.readingTime,
      viewCount: blog.viewCount + 1, // Include the increment
      shareCount: blog.shareCount,
      isFeatured: blog.isFeatured,
      allowComments: blog.allowComments,
      author: blog.authorId ? {
        firstName: (blog.authorId as any).firstName,
        lastName: (blog.authorId as any).lastName,
        name: `${(blog.authorId as any).firstName} ${(blog.authorId as any).lastName}`,
      } : null,
      seo: {
        title: blog.seo?.seoTitle || blog.title,
        description: blog.seo?.seoDescription || blog.excerpt,
        ogTitle: blog.seo?.ogTitle || blog.title,
        ogDescription: blog.seo?.ogDescription || blog.excerpt,
        ogImage: blog.seo?.ogImage || blog.featuredImage,
        canonicalUrl: blog.seo?.canonicalUrl,
        focusKeyword: blog.seo?.focusKeyword,
        noIndex: blog.seo?.noIndex || false,
        noFollow: blog.seo?.noFollow || false,
      },
      website: {
        name: website.name,
        domain: website.domain,
        seoSettings: website.seoSettings,
      },
    };
  }

  async getCategories(website: WebsiteDocument): Promise<string[]> {
    const categories = await this.blogModel.aggregate([
      {
        $match: {
          websiteId: website._id.toString(),
          status: BlogStatus.PUBLISHED,
          publishedAt: { $lte: new Date() },
        },
      },
      { $unwind: '$categories' },
      { $group: { _id: '$categories', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $project: { _id: 1 } },
    ]);

    return categories.map(cat => cat._id);
  }

  async getTags(website: WebsiteDocument): Promise<string[]> {
    const tags = await this.blogModel.aggregate([
      {
        $match: {
          websiteId: website._id.toString(),
          status: BlogStatus.PUBLISHED,
          publishedAt: { $lte: new Date() },
        },
      },
      { $unwind: '$tags' },
      { $group: { _id: '$tags', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 50 }, // Limit to top 50 tags
      { $project: { _id: 1 } },
    ]);

    return tags.map(tag => tag._id);
  }

  async incrementShareCount(website: WebsiteDocument, blogId: string): Promise<void> {
    await this.blogModel.findOneAndUpdate(
      {
        _id: blogId,
        websiteId: website._id.toString(),
        status: BlogStatus.PUBLISHED
      },
      { $inc: { shareCount: 1 } }
    );
  }

  async getWebsiteInfo(website: WebsiteDocument): Promise<any> {
    const blogCount = await this.blogModel.countDocuments({
      websiteId: website._id.toString(),
      status: BlogStatus.PUBLISHED,
      publishedAt: { $lte: new Date() },
    });

    return {
      name: website.name,
      domain: website.domain,
      description: website.description,
      theme: website.theme,
      blogCount,
      seoSettings: website.seoSettings,
    };
  }
}
