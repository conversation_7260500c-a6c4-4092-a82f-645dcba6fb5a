import { 
  Injectable, 
  CanActivate, 
  ExecutionContext, 
  UnauthorizedException 
} from '@nestjs/common';
import { WebsitesService } from '../../websites/websites.service';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  constructor(private websitesService: WebsitesService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const apiKey = request.headers['x-api-key'] || request.query.apiKey;

    if (!apiKey) {
      throw new UnauthorizedException('API key is required');
    }

    const website = await this.websitesService.findByApiKey(apiKey);
    if (!website) {
      throw new UnauthorizedException('Invalid API key');
    }

    // Attach website to request for use in controllers
    request.website = website;
    return true;
  }
}
