import {
  Controller,
  Get,
  Post,
  Param,
  Query,
  UseGuards,
  NotFoundException,
  Headers,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiSecurity,
  ApiHeader,
} from '@nestjs/swagger';
import { PublicService } from './public.service';
import { PublicBlogQueryDto } from './dto/public-query.dto';
import { ApiKeyGuard } from './guards/api-key.guard';
import { CurrentWebsite } from './decorators/current-website.decorator';
import { WebsiteDocument } from '../websites/schemas/website.schema';
import { Public } from '../../common/decorators/public.decorator';

@ApiTags('Public API')
@Controller('public')
@UseGuards(ApiKeyGuard)
@ApiSecurity('api-key')
@ApiHeader({
  name: 'X-API-Key',
  description: 'Website API key for authentication',
  required: true,
})
@Public()
export class PublicController {
  constructor(private readonly publicService: PublicService) {}

  @Get('website')
  @ApiOperation({ 
    summary: 'Get website information',
    description: 'Returns basic information about the website associated with the API key'
  })
  @ApiResponse({
    status: 200,
    description: 'Website information retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        domain: { type: 'string' },
        description: { type: 'string' },
        theme: { type: 'string' },
        blogCount: { type: 'number' },
        seoSettings: { type: 'object' },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Invalid API key' })
  async getWebsiteInfo(@CurrentWebsite() website: WebsiteDocument) {
    const websiteInfo = await this.publicService.getWebsiteInfo(website);
    return {
      website: websiteInfo,
      message: 'Website information retrieved successfully',
    };
  }

  @Get('blogs')
  @ApiOperation({ 
    summary: 'Get published blogs',
    description: 'Returns a paginated list of published blogs for the website'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (max 50)' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'category', required: false, type: String, description: 'Filter by category' })
  @ApiQuery({ name: 'tags', required: false, type: String, description: 'Filter by tags (comma-separated)' })
  @ApiQuery({ name: 'sortBy', required: false, enum: ['publishedAt', 'title', 'viewCount'], description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Sort order' })
  @ApiQuery({ name: 'featured', required: false, type: Boolean, description: 'Include only featured blogs' })
  @ApiResponse({
    status: 200,
    description: 'Blogs retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              title: { type: 'string' },
              slug: { type: 'string' },
              excerpt: { type: 'string' },
              featuredImage: { type: 'string' },
              publishedAt: { type: 'string', format: 'date-time' },
              categories: { type: 'array', items: { type: 'string' } },
              tags: { type: 'array', items: { type: 'string' } },
              readingTime: { type: 'number' },
              viewCount: { type: 'number' },
              shareCount: { type: 'number' },
              isFeatured: { type: 'boolean' },
              author: { type: 'object' },
              seo: { type: 'object' },
            },
          },
        },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' },
        hasNext: { type: 'boolean' },
        hasPrev: { type: 'boolean' },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Invalid API key' })
  async getBlogs(
    @Query() query: PublicBlogQueryDto,
    @CurrentWebsite() website: WebsiteDocument,
  ) {
    const result = await this.publicService.getBlogs(website, query);
    return {
      ...result,
      message: 'Blogs retrieved successfully',
    };
  }

  @Get('blogs/:slug')
  @ApiOperation({ 
    summary: 'Get blog by slug',
    description: 'Returns a single blog post by its slug'
  })
  @ApiParam({ name: 'slug', description: 'Blog slug' })
  @ApiResponse({
    status: 200,
    description: 'Blog retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        blog: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            title: { type: 'string' },
            slug: { type: 'string' },
            content: { type: 'string' },
            excerpt: { type: 'string' },
            featuredImage: { type: 'string' },
            publishedAt: { type: 'string', format: 'date-time' },
            categories: { type: 'array', items: { type: 'string' } },
            tags: { type: 'array', items: { type: 'string' } },
            readingTime: { type: 'number' },
            viewCount: { type: 'number' },
            shareCount: { type: 'number' },
            isFeatured: { type: 'boolean' },
            allowComments: { type: 'boolean' },
            author: { type: 'object' },
            seo: { type: 'object' },
            website: { type: 'object' },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Blog not found' })
  @ApiResponse({ status: 401, description: 'Invalid API key' })
  async getBlogBySlug(
    @Param('slug') slug: string,
    @CurrentWebsite() website: WebsiteDocument,
  ) {
    const blog = await this.publicService.getBlogBySlug(website, slug);
    
    if (!blog) {
      throw new NotFoundException('Blog not found');
    }

    return {
      blog,
      message: 'Blog retrieved successfully',
    };
  }

  @Get('categories')
  @ApiOperation({ 
    summary: 'Get all categories',
    description: 'Returns a list of all categories used in published blogs'
  })
  @ApiResponse({
    status: 200,
    description: 'Categories retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        categories: {
          type: 'array',
          items: { type: 'string' },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Invalid API key' })
  async getCategories(@CurrentWebsite() website: WebsiteDocument) {
    const categories = await this.publicService.getCategories(website);
    return {
      categories,
      message: 'Categories retrieved successfully',
    };
  }

  @Get('tags')
  @ApiOperation({ 
    summary: 'Get all tags',
    description: 'Returns a list of all tags used in published blogs'
  })
  @ApiResponse({
    status: 200,
    description: 'Tags retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        tags: {
          type: 'array',
          items: { type: 'string' },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Invalid API key' })
  async getTags(@CurrentWebsite() website: WebsiteDocument) {
    const tags = await this.publicService.getTags(website);
    return {
      tags,
      message: 'Tags retrieved successfully',
    };
  }

  @Post('blogs/:id/share')
  @ApiOperation({ 
    summary: 'Increment blog share count',
    description: 'Increments the share count for a blog post'
  })
  @ApiParam({ name: 'id', description: 'Blog ID' })
  @ApiResponse({
    status: 200,
    description: 'Share count incremented successfully',
  })
  @ApiResponse({ status: 401, description: 'Invalid API key' })
  async incrementShareCount(
    @Param('id') blogId: string,
    @CurrentWebsite() website: WebsiteDocument,
  ) {
    await this.publicService.incrementShareCount(website, blogId);
    return {
      message: 'Share count incremented successfully',
    };
  }
}
