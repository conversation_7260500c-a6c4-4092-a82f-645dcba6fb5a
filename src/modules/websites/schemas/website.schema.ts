import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { User } from '../../users/schemas/user.schema';

export type WebsiteDocument = Website & Document;

export enum WebsiteTheme {
  MINIMAL = 'minimal',
  MAGAZINE = 'magazine',
  CORPORATE = 'corporate',
}

@Schema({ _id: false })
export class SeoSettings {
  @Prop({ type: String })
  defaultTitle?: string;

  @Prop({ type: String })
  defaultDescription?: string;

  @Prop({ type: String })
  favicon?: string;

  @Prop({ type: String })
  ogImage?: string;

  @Prop({ type: String })
  googleAnalyticsId?: string;

  @Prop({ type: String })
  googleSearchConsoleId?: string;
}

const SeoSettingsSchema = SchemaFactory.createForClass(SeoSettings);

@Schema({ timestamps: true })
export class Website {
  @Prop({ required: true, trim: true })
  name: string;

  @Prop({ required: true, trim: true, lowercase: true })
  domain: string;

  @Prop({ required: true, unique: true })
  apiKey: string;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  ownerId: Types.ObjectId;

  @Prop({ type: SeoSettingsSchema, default: {} })
  seoSettings: SeoSettings;

  @Prop({ 
    type: String, 
    enum: Object.values(WebsiteTheme), 
    default: WebsiteTheme.MINIMAL 
  })
  theme: WebsiteTheme;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: 0 })
  blogCount: number;

  @Prop({ type: String })
  description?: string;

  @Prop({ type: String })
  logo?: string;

  @Prop({ type: [String], default: [] })
  allowedDomains: string[];

  @Prop({ type: Object, default: {} })
  customSettings: Record<string, any>;
}

export const WebsiteSchema = SchemaFactory.createForClass(Website);

// Indexes for performance
WebsiteSchema.index({ apiKey: 1 });
WebsiteSchema.index({ ownerId: 1 });
WebsiteSchema.index({ domain: 1 });
WebsiteSchema.index({ isActive: 1 });
WebsiteSchema.index({ createdAt: -1 });

// Compound indexes
WebsiteSchema.index({ ownerId: 1, isActive: 1 });
WebsiteSchema.index({ domain: 1, isActive: 1 });
