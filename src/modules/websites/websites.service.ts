import { 
  Injectable, 
  NotFoundException, 
  ConflictException,
  ForbiddenException 
} from '@nestjs/common';
import { Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Website, WebsiteDocument } from './schemas/website.schema';
import { CreateWebsiteDto } from './dto/create-website.dto';
import { UpdateWebsiteDto } from './dto/update-website.dto';
import { QueryDto } from '../../common/dto/query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination.dto';
import { UserDocument } from '../users/schemas/user.schema';
import { UserRole } from '../../common/decorators/roles.decorator';
import { generateApiKey } from '../../common/utils/api-key.util';

@Injectable()
export class WebsitesService {
  constructor(
    @InjectModel(Website.name) private websiteModel: Model<WebsiteDocument>,
  ) {}

  async create(
    createWebsiteDto: CreateWebsiteDto,
    currentUser: UserDocument
  ): Promise<WebsiteDocument> {
    const { domain, ...websiteData } = createWebsiteDto;

    // Check if domain already exists
    const existingWebsite = await this.websiteModel.findOne({ domain });
    if (existingWebsite) {
      throw new ConflictException('Website with this domain already exists');
    }

    // Generate unique API key
    let apiKey: string;
    let isUnique = false;
    
    while (!isUnique) {
      apiKey = generateApiKey();
      const existingApiKey = await this.websiteModel.findOne({ apiKey });
      if (!existingApiKey) {
        isUnique = true;
      }
    }

    const website = new this.websiteModel({
      ...websiteData,
      domain: domain.toLowerCase(),
      apiKey,
      ownerId: currentUser._id,
    });

    return website.save();
  }

  async findAll(
    query: QueryDto,
    currentUser: UserDocument
  ): Promise<PaginationResponseDto<WebsiteDocument>> {
    const { page = 1, limit = 10, search, sortBy = 'createdAt', sortOrder = 'desc' } = query;
    
    const filter: any = {};
    
    // Non-admin users can only see their own websites
    if (currentUser.role !== UserRole.ADMIN) {
      filter.ownerId = currentUser._id;
    }
    
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { domain: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }

    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [websites, total] = await Promise.all([
      this.websiteModel
        .find(filter)
        .populate('ownerId', 'firstName lastName email')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.websiteModel.countDocuments(filter),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: websites,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async findOne(id: string, currentUser: UserDocument): Promise<WebsiteDocument> {
    const website = await this.websiteModel
      .findById(id)
      .populate('ownerId', 'firstName lastName email');
    
    if (!website) {
      throw new NotFoundException('Website not found');
    }

    // Check permissions
    if (
      currentUser.role !== UserRole.ADMIN && 
      website.ownerId._id.toString() !== currentUser._id.toString()
    ) {
      throw new ForbiddenException('You can only access your own websites');
    }

    return website;
  }

  async findByApiKey(apiKey: string): Promise<WebsiteDocument | null> {
    return this.websiteModel.findOne({ apiKey, isActive: true });
  }

  async update(
    id: string, 
    updateWebsiteDto: UpdateWebsiteDto,
    currentUser: UserDocument
  ): Promise<WebsiteDocument> {
    const website = await this.findOne(id, currentUser);

    // Check if domain is being changed and if it already exists
    if (updateWebsiteDto.domain && updateWebsiteDto.domain !== website.domain) {
      const existingWebsite = await this.websiteModel.findOne({ 
        domain: updateWebsiteDto.domain.toLowerCase(),
        _id: { $ne: id }
      });
      
      if (existingWebsite) {
        throw new ConflictException('Website with this domain already exists');
      }
      
      updateWebsiteDto.domain = updateWebsiteDto.domain.toLowerCase();
    }

    Object.assign(website, updateWebsiteDto);
    return website.save();
  }

  async remove(id: string, currentUser: UserDocument): Promise<void> {
    const website = await this.findOne(id, currentUser);
    await this.websiteModel.findByIdAndDelete(id);
  }

  async regenerateApiKey(id: string, currentUser: UserDocument): Promise<WebsiteDocument> {
    const website = await this.findOne(id, currentUser);

    // Generate new unique API key
    let apiKey: string;
    let isUnique = false;
    
    while (!isUnique) {
      apiKey = generateApiKey();
      const existingApiKey = await this.websiteModel.findOne({ apiKey });
      if (!existingApiKey) {
        isUnique = true;
      }
    }

    website.apiKey = apiKey;
    return website.save();
  }

  async getStats(currentUser: UserDocument): Promise<any> {
    const filter: any = {};
    
    // Non-admin users can only see their own website stats
    if (currentUser.role !== UserRole.ADMIN) {
      filter.ownerId = currentUser._id;
    }

    const [total, active, byTheme] = await Promise.all([
      this.websiteModel.countDocuments(filter),
      this.websiteModel.countDocuments({ ...filter, isActive: true }),
      this.websiteModel.aggregate([
        { $match: filter },
        {
          $group: {
            _id: '$theme',
            count: { $sum: 1 },
          },
        },
      ]),
    ]);

    return {
      total,
      active,
      inactive: total - active,
      byTheme: byTheme.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
    };
  }

  async getUserWebsites(userId: string): Promise<WebsiteDocument[]> {
    console.log('🔍 getUserWebsites called with userId:', userId, 'type:', typeof userId);
    
    // Convert string to ObjectId for proper comparison
    const userObjectId = new Types.ObjectId(userId);
    console.log('🔄 Converted to ObjectId:', userObjectId);
    
    // First, let's check if there are any websites at all
    const allWebsites = await this.websiteModel.find({}).select('_id name domain ownerId isActive').exec();
    console.log('📊 All websites in database:', allWebsites.map(w => ({
      id: w._id,
      name: w.name,
      ownerId: w.ownerId,
      ownerIdType: typeof w.ownerId,
      isActive: w.isActive
    })));
    
    // Try with ObjectId comparison
    const userWebsites = await this.websiteModel
      .find({ ownerId: userObjectId, isActive: true })
      .select('_id name domain description theme ownerId')
      .sort({ name: 1 })
      .exec();
    
    console.log('🎯 User websites found (ObjectId):', userWebsites);
    
    // Also try with string comparison
    const userWebsitesString = await this.websiteModel
      .find({ ownerId: userId, isActive: true })
      .select('_id name domain description theme ownerId')
      .sort({ name: 1 })
      .exec();
    
    console.log('🎯 User websites found (String):', userWebsitesString);
    
    // Also try without the isActive filter
    const userWebsitesAll = await this.websiteModel
      .find({ ownerId: userObjectId })
      .select('_id name domain description theme ownerId isActive')
      .sort({ name: 1 })
      .exec();
    
    console.log('🔄 User websites (all active states):', userWebsitesAll);
    
    // Return whichever query found results
    if (userWebsites.length > 0) {
      console.log('✅ Returning ObjectId results');
      return userWebsites;
    } else if (userWebsitesString.length > 0) {
      console.log('✅ Returning String results');
      return userWebsitesString;
    } else if (userWebsitesAll.length > 0) {
      console.log('✅ Returning all results (ignoring isActive)');
      return userWebsitesAll;
    } else {
      console.log('❌ No websites found for user');
      return [];
    }
  }

  async incrementBlogCount(websiteId: string): Promise<void> {
    await this.websiteModel.findByIdAndUpdate(
      websiteId,
      { $inc: { blogCount: 1 } }
    );
  }

  async decrementBlogCount(websiteId: string): Promise<void> {
    await this.websiteModel.findByIdAndUpdate(
      websiteId,
      { $inc: { blogCount: -1 } }
    );
  }
}
