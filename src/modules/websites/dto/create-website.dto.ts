import { ApiProperty } from '@nestjs/swagger';
import { 
  IsString, 
  IsEnum, 
  IsOptional, 
  IsBoolean, 
  IsUrl,
  IsArray,
  ValidateNested,
  IsObject
} from 'class-validator';
import { Type } from 'class-transformer';
import { WebsiteTheme } from '../schemas/website.schema';

export class SeoSettingsDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  defaultTitle?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  defaultDescription?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUrl()
  favicon?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUrl()
  ogImage?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  googleAnalyticsId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  googleSearchConsoleId?: string;
}

export class CreateWebsiteDto {
  @ApiProperty({ 
    description: 'Website name',
    example: 'My Awesome Blog' 
  })
  @IsString()
  name: string;

  @ApiProperty({ 
    description: 'Website domain',
    example: 'myawesomeblog.com' 
  })
  @IsString()
  domain: string;

  @ApiProperty({ 
    description: 'Website theme',
    enum: WebsiteTheme,
    default: WebsiteTheme.MINIMAL,
    required: false 
  })
  @IsOptional()
  @IsEnum(WebsiteTheme)
  theme?: WebsiteTheme;

  @ApiProperty({ 
    description: 'Website description',
    required: false 
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ 
    description: 'Website logo URL',
    required: false 
  })
  @IsOptional()
  @IsUrl()
  logo?: string;

  @ApiProperty({ 
    description: 'SEO settings',
    type: SeoSettingsDto,
    required: false 
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SeoSettingsDto)
  seoSettings?: SeoSettingsDto;

  @ApiProperty({ 
    description: 'Allowed domains for CORS',
    type: [String],
    required: false 
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  allowedDomains?: string[];

  @ApiProperty({ 
    description: 'Custom settings object',
    required: false 
  })
  @IsOptional()
  @IsObject()
  customSettings?: Record<string, any>;
}
