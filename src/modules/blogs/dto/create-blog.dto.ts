import { ApiProperty } from '@nestjs/swagger';
import { 
  IsString, 
  IsEnum, 
  IsOptional, 
  IsBoolean, 
  IsUrl,
  IsArray,
  ValidateNested,
  IsDateString,
  IsNumber,
  Min,
  Max,
  IsMongoId
} from 'class-validator';
import { Type } from 'class-transformer';
import { BlogStatus } from '../schemas/blog.schema';

export class SeoDataDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  seoTitle?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  seoDescription?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  focusKeyword?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUrl()
  canonicalUrl?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  ogTitle?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  ogDescription?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUrl()
  ogImage?: string;

  @ApiProperty({ required: false, default: false })
  @IsOptional()
  @IsBoolean()
  noIndex?: boolean;

  @ApiProperty({ required: false, default: false })
  @IsOptional()
  @IsBoolean()
  noFollow?: boolean;

  @ApiProperty({ required: false, minimum: 0, maximum: 100 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  seoScore?: number;
}

export class CreateBlogDto {
  @ApiProperty({ 
    description: 'Blog title',
    example: 'How to Build Amazing Websites' 
  })
  @IsString()
  title: string;

  @ApiProperty({ 
    description: 'Blog slug (URL-friendly)',
    example: 'how-to-build-amazing-websites' 
  })
  @IsString()
  slug: string;

  @ApiProperty({ 
    description: 'Blog content (HTML)',
    example: '<p>This is the blog content...</p>' 
  })
  @IsString()
  content: string;

  @ApiProperty({ 
    description: 'Blog excerpt/summary',
    example: 'Learn the fundamentals of building amazing websites...' 
  })
  @IsString()
  excerpt: string;

  @ApiProperty({ 
    description: 'Website ID this blog belongs to' 
  })
  @IsMongoId()
  websiteId: string;

  @ApiProperty({ 
    description: 'Featured image URL',
    required: false 
  })
  @IsOptional()
  @IsUrl()
  featuredImage?: string;

  @ApiProperty({ 
    description: 'Blog status',
    enum: BlogStatus,
    default: BlogStatus.DRAFT,
    required: false 
  })
  @IsOptional()
  @IsEnum(BlogStatus)
  status?: BlogStatus;

  @ApiProperty({ 
    description: 'SEO data',
    type: SeoDataDto,
    required: false 
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SeoDataDto)
  seo?: SeoDataDto;

  @ApiProperty({ 
    description: 'Blog categories',
    type: [String],
    required: false 
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  categories?: string[];

  @ApiProperty({ 
    description: 'Blog tags',
    type: [String],
    required: false 
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({ 
    description: 'Scheduled publish date',
    required: false 
  })
  @IsOptional()
  @IsDateString()
  scheduledAt?: string;

  @ApiProperty({ 
    description: 'Reading time in minutes',
    required: false 
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  readingTime?: number;

  @ApiProperty({ 
    description: 'Whether this is a featured blog',
    default: false,
    required: false 
  })
  @IsOptional()
  @IsBoolean()
  isFeatured?: boolean;

  @ApiProperty({ 
    description: 'Whether comments are allowed',
    default: true,
    required: false 
  })
  @IsOptional()
  @IsBoolean()
  allowComments?: boolean;
}
