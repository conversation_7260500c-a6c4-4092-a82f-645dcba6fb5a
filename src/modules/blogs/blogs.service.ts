import { 
  Injectable, 
  NotFoundException, 
  ConflictException,
  ForbiddenException,
  BadRequestException 
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Blog, BlogDocument, BlogStatus } from './schemas/blog.schema';
import { CreateBlogDto } from './dto/create-blog.dto';
import { UpdateBlogDto } from './dto/update-blog.dto';
import { QueryDto } from '../../common/dto/query.dto';
import { PaginationResponseDto } from '../../common/dto/pagination.dto';
import { UserDocument } from '../users/schemas/user.schema';
import { WebsitesService } from '../websites/websites.service';
import { UserRole } from '../../common/decorators/roles.decorator';
import { generateSlug, generateUniqueSlug } from '../../common/utils/slug.util';
import { calculateReadingTime } from '../../common/utils/reading-time.util';

@Injectable()
export class BlogsService {
  constructor(
    @InjectModel(Blog.name) private blogModel: Model<BlogDocument>,
    private websitesService: WebsitesService,
  ) {}

  async create(
    createBlogDto: CreateBlogDto,
    currentUser: UserDocument
  ): Promise<BlogDocument> {
    const { websiteId, slug, content, ...blogData } = createBlogDto;

    // Verify website exists and user has access
    await this.websitesService.findOne(websiteId, currentUser);

    // Generate or validate slug
    let finalSlug = slug;
    if (!finalSlug) {
      finalSlug = generateSlug(createBlogDto.title);
    } else {
      finalSlug = generateSlug(finalSlug);
    }

    // Ensure slug is unique within the website
    const existingSlugs = await this.blogModel
      .find({ websiteId, slug: { $regex: `^${finalSlug}` } })
      .select('slug')
      .exec();
    
    const slugList = existingSlugs.map(blog => blog.slug);
    finalSlug = generateUniqueSlug(finalSlug, slugList);

    // Calculate reading time
    const readingTime = calculateReadingTime(content);

    const blog = new this.blogModel({
      ...blogData,
      slug: finalSlug,
      content,
      websiteId,
      authorId: currentUser._id,
      readingTime,
    });

    const savedBlog = await blog.save();

    // Increment blog count for website
    await this.websitesService.incrementBlogCount(websiteId);

    return savedBlog;
  }

  async findAll(
    query: QueryDto,
    currentUser: UserDocument,
    websiteId?: string
  ): Promise<PaginationResponseDto<BlogDocument>> {
    const { page = 1, limit = 10, search, sortBy = 'createdAt', sortOrder = 'desc' } = query;
    
    const filter: any = {};
    
    if (websiteId) {
      // Verify user has access to this website
      await this.websitesService.findOne(websiteId, currentUser);
      filter.websiteId = websiteId.toString();
    } else if (currentUser.role !== UserRole.ADMIN) {
      // Non-admin users can only see blogs from their websites
      const userWebsites = await this.websitesService.getUserWebsites(
        currentUser._id.toString()
      );
      filter.websiteId = { $in: userWebsites.map(w => w._id.toString()) };
    }
    
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { excerpt: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } },
        { categories: { $in: [new RegExp(search, 'i')] } },
        { tags: { $in: [new RegExp(search, 'i')] } },
      ];
    }

    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    const [blogs, total] = await Promise.all([
      this.blogModel
        .find(filter)
        .populate('websiteId', 'name domain')
        .populate('authorId', 'firstName lastName email')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.blogModel.countDocuments(filter),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: blogs,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async findOne(id: string, currentUser: UserDocument): Promise<BlogDocument> {
    const blog = await this.blogModel
      .findById(id)
      .populate('websiteId', 'name domain ownerId')
      .populate('authorId', 'firstName lastName email');
    
    if (!blog) {
      throw new NotFoundException('Blog not found');
    }

    // Check permissions
    if (
      currentUser.role !== UserRole.ADMIN && 
      (blog.websiteId as any).ownerId.toString() !== currentUser._id.toString()
    ) {
      throw new ForbiddenException('You can only access blogs from your websites');
    }

    return blog;
  }

  async findBySlug(
    websiteId: string, 
    slug: string, 
    includeUnpublished = false
  ): Promise<BlogDocument | null> {
    const filter: any = { websiteId, slug };
    
    if (!includeUnpublished) {
      filter.status = BlogStatus.PUBLISHED;
      filter.publishedAt = { $lte: new Date() };
    }

    return this.blogModel
      .findOne(filter)
      .populate('websiteId', 'name domain seoSettings')
      .populate('authorId', 'firstName lastName')
      .exec();
  }

  async update(
    id: string, 
    updateBlogDto: UpdateBlogDto,
    currentUser: UserDocument
  ): Promise<BlogDocument> {
    const blog = await this.findOne(id, currentUser);

    // Update slug if title changed
    if (updateBlogDto.title && updateBlogDto.title !== blog.title) {
      if (!updateBlogDto.slug) {
        updateBlogDto.slug = generateSlug(updateBlogDto.title);
      }
    }

    // Validate slug uniqueness if changed
    if (updateBlogDto.slug && updateBlogDto.slug !== blog.slug) {
      const finalSlug = generateSlug(updateBlogDto.slug);
      const existingSlugs = await this.blogModel
        .find({ 
          websiteId: blog.websiteId, 
          slug: { $regex: `^${finalSlug}` },
          _id: { $ne: id }
        })
        .select('slug')
        .exec();
      
      const slugList = existingSlugs.map(b => b.slug);
      updateBlogDto.slug = generateUniqueSlug(finalSlug, slugList);
    }

    // Recalculate reading time if content changed
    if (updateBlogDto.content) {
      updateBlogDto.readingTime = calculateReadingTime(updateBlogDto.content);
    }

    Object.assign(blog, updateBlogDto);
    return blog.save();
  }

  async remove(id: string, currentUser: UserDocument): Promise<void> {
    const blog = await this.findOne(id, currentUser);
    
    // Decrement blog count for website
    await this.websitesService.decrementBlogCount(blog.websiteId._id.toString());
    
    await this.blogModel.findByIdAndDelete(id);
  }

  async publish(id: string, currentUser: UserDocument): Promise<BlogDocument> {
    const blog = await this.findOne(id, currentUser);

    if (blog.status === BlogStatus.PUBLISHED) {
      throw new BadRequestException('Blog is already published');
    }

    blog.status = BlogStatus.PUBLISHED;
    blog.publishedAt = new Date();
    
    return blog.save();
  }

  async schedule(
    id: string, 
    scheduledAt: Date, 
    currentUser: UserDocument
  ): Promise<BlogDocument> {
    const blog = await this.findOne(id, currentUser);

    if (scheduledAt <= new Date()) {
      throw new BadRequestException('Scheduled date must be in the future');
    }

    blog.status = BlogStatus.SCHEDULED;
    blog.scheduledAt = scheduledAt;
    
    return blog.save();
  }

  async incrementViewCount(id: string): Promise<void> {
    await this.blogModel.findByIdAndUpdate(id, { $inc: { viewCount: 1 } });
  }

  async incrementShareCount(id: string): Promise<void> {
    await this.blogModel.findByIdAndUpdate(id, { $inc: { shareCount: 1 } });
  }

  async getStats(currentUser: UserDocument, websiteId?: string): Promise<any> {
    const filter: any = {};
    
    if (websiteId) {
      await this.websitesService.findOne(websiteId, currentUser);
      filter.websiteId = websiteId.toString();
    } else if (currentUser.role !== UserRole.ADMIN) {
      const userWebsites = await this.websitesService.getUserWebsites(
        currentUser._id.toString()
      );
      filter.websiteId = { $in: userWebsites.map(w => w._id.toString()) };
    }

    const [total, published, draft, scheduled, totalViews] = await Promise.all([
      this.blogModel.countDocuments(filter),
      this.blogModel.countDocuments({ ...filter, status: BlogStatus.PUBLISHED }),
      this.blogModel.countDocuments({ ...filter, status: BlogStatus.DRAFT }),
      this.blogModel.countDocuments({ ...filter, status: BlogStatus.SCHEDULED }),
      this.blogModel.aggregate([
        { $match: filter },
        { $group: { _id: null, totalViews: { $sum: '$viewCount' } } }
      ]),
    ]);

    return {
      total,
      published,
      draft,
      scheduled,
      totalViews: totalViews[0]?.totalViews || 0,
    };
  }
}
