import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { BlogsService } from './blogs.service';
import { CreateBlogDto } from './dto/create-blog.dto';
import { UpdateBlogDto } from './dto/update-blog.dto';
import { QueryDto } from '../../common/dto/query.dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { UserDocument } from '../users/schemas/user.schema';

@ApiTags('Blogs')
@Controller('blogs')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class BlogsController {
  constructor(private readonly blogsService: BlogsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new blog' })
  @ApiResponse({
    status: 201,
    description: 'Blog successfully created',
  })
  @ApiResponse({ status: 409, description: 'Blog slug already exists' })
  async create(
    @Body() createBlogDto: CreateBlogDto,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const blog = await this.blogsService.create(createBlogDto, currentUser);
    return {
      blog,
      message: 'Blog created successfully',
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all blogs with pagination' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
  @ApiQuery({ name: 'websiteId', required: false, type: String })
  @ApiResponse({
    status: 200,
    description: 'Blogs retrieved successfully',
  })
  async findAll(
    @Query() query: QueryDto,
    @Query('websiteId') websiteId: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const result = await this.blogsService.findAll(query, currentUser, websiteId);
    return {
      ...result,
      message: 'Blogs retrieved successfully',
    };
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get blog statistics' })
  @ApiQuery({ name: 'websiteId', required: false, type: String })
  @ApiResponse({
    status: 200,
    description: 'Blog statistics retrieved successfully',
  })
  async getStats(
    @Query('websiteId') websiteId: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const stats = await this.blogsService.getStats(currentUser, websiteId);
    return {
      stats,
      message: 'Blog statistics retrieved successfully',
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get blog by ID' })
  @ApiParam({ name: 'id', description: 'Blog ID' })
  @ApiResponse({
    status: 200,
    description: 'Blog retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Blog not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async findOne(
    @Param('id') id: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const blog = await this.blogsService.findOne(id, currentUser);
    return {
      blog,
      message: 'Blog retrieved successfully',
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update blog' })
  @ApiParam({ name: 'id', description: 'Blog ID' })
  @ApiResponse({
    status: 200,
    description: 'Blog updated successfully',
  })
  @ApiResponse({ status: 404, description: 'Blog not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async update(
    @Param('id') id: string,
    @Body() updateBlogDto: UpdateBlogDto,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const blog = await this.blogsService.update(id, updateBlogDto, currentUser);
    return {
      blog,
      message: 'Blog updated successfully',
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete blog' })
  @ApiParam({ name: 'id', description: 'Blog ID' })
  @ApiResponse({
    status: 200,
    description: 'Blog deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Blog not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async remove(
    @Param('id') id: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    await this.blogsService.remove(id, currentUser);
    return {
      message: 'Blog deleted successfully',
    };
  }

  @Post(':id/publish')
  @ApiOperation({ summary: 'Publish blog' })
  @ApiParam({ name: 'id', description: 'Blog ID' })
  @ApiResponse({
    status: 200,
    description: 'Blog published successfully',
  })
  @ApiResponse({ status: 404, description: 'Blog not found' })
  @ApiResponse({ status: 400, description: 'Blog already published' })
  async publish(
    @Param('id') id: string,
    @CurrentUser() currentUser: UserDocument,
  ) {
    const blog = await this.blogsService.publish(id, currentUser);
    return {
      blog,
      message: 'Blog published successfully',
    };
  }

  @Post(':id/schedule')
  @ApiOperation({ summary: 'Schedule blog for publishing' })
  @ApiParam({ name: 'id', description: 'Blog ID' })
  @ApiResponse({
    status: 200,
    description: 'Blog scheduled successfully',
  })
  @ApiResponse({ status: 404, description: 'Blog not found' })
  @ApiResponse({ status: 400, description: 'Invalid schedule date' })
  async schedule(
    @Param('id') id: string,
    @Body() body: { scheduledAt: string },
    @CurrentUser() currentUser: UserDocument,
  ) {
    const scheduledAt = new Date(body.scheduledAt);
    const blog = await this.blogsService.schedule(id, scheduledAt, currentUser);
    return {
      blog,
      message: 'Blog scheduled successfully',
    };
  }

  @Post(':id/view')
  @ApiOperation({ summary: 'Increment blog view count' })
  @ApiParam({ name: 'id', description: 'Blog ID' })
  @ApiResponse({
    status: 200,
    description: 'View count incremented',
  })
  async incrementView(@Param('id') id: string) {
    await this.blogsService.incrementViewCount(id);
    return {
      message: 'View count incremented',
    };
  }

  @Post(':id/share')
  @ApiOperation({ summary: 'Increment blog share count' })
  @ApiParam({ name: 'id', description: 'Blog ID' })
  @ApiResponse({
    status: 200,
    description: 'Share count incremented',
  })
  async incrementShare(@Param('id') id: string) {
    await this.blogsService.incrementShareCount(id);
    return {
      message: 'Share count incremented',
    };
  }
}
