# Test Environment Configuration
NODE_ENV=test

# Database
MONGODB_URI=mongodb://localhost:27017/test

# JWT Configuration
JWT_SECRET=test-jwt-secret-key-for-integration-testing
JWT_EXPIRES_IN=1h

# API Configuration
PORT=3001
API_PREFIX=api

# Security
BCRYPT_ROUNDS=4

# Test Settings
TEST_VERBOSE=false
TEST_TIMEOUT=30000

# Cloudinary (mock for testing)
CLOUDINARY_CLOUD_NAME=test-cloud
CLOUDINARY_API_KEY=test-api-key
CLOUDINARY_API_SECRET=test-api-secret