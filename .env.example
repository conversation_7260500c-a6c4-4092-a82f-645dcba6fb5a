# Database Configuration
MONGODB_URI=mongodb+srv://zohaib:<EMAIL>/creasoft-cms

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d

# Server Configuration
PORT=8000
NODE_ENV=development

# Frontend URL for CORS
FRONTEND_URL=http://localhost:3000

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=dzuxnbt8u
CLOUDINARY_API_KEY=229778449983798
CLOUDINARY_API_SECRET=J3kXcIIz_hNTYgzkzRfGGau2loM

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# File Upload Limits
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp
