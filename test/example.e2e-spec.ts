import { INestApplication } from '@nestjs/common';
import { TestEnvironment } from './utils/test-environment.util';
import { IntegrationTestConfig } from './integration-test.config';

describe('Integration Test Setup Verification (e2e)', () => {
  let app: INestApplication;
  let testEnv: TestEnvironment;

  beforeAll(async () => {
    testEnv = await IntegrationTestConfig.setupIntegrationTest();
    app = testEnv.getApp();
  });

  afterAll(async () => {
    await IntegrationTestConfig.teardownIntegrationTest();
  });

  afterEach(async () => {
    await IntegrationTestConfig.clearTestData();
  });

  describe('Test Environment Setup', () => {
    it('should have a running NestJS application', () => {
      expect(app).toBeDefined();
      expect(app.getHttpServer()).toBeDefined();
    });

    it('should have database connection', async () => {
      const request = testEnv.request();
      // Test a simple endpoint to verify the app is running
      const response = await request.get('/api');
      // The response might be 404 but that means the server is responding
      expect([200, 404]).toContain(response.status);
    });

    it('should be able to make HTTP requests', async () => {
      const request = testEnv.request();
      const response = await request.get('/api/health').expect((res) => {
        // We expect either a successful response or a 404 (endpoint doesn't exist yet)
        expect([200, 404]).toContain(res.status);
      });
    });

    it('should clear database between tests', async () => {
      // This test verifies that database cleanup works
      // We'll implement actual database operations in later tasks
      expect(true).toBe(true);
    });
  });

  describe('Test Configuration', () => {
    it('should have correct test constants', () => {
      const constants = IntegrationTestConfig.getTestConstants();
      
      expect(constants.DEFAULT_TIMEOUT).toBe(30000);
      expect(constants.TEST_API_KEY_PREFIX).toBe('creasoft_');
      expect(constants.HTTP_STATUS.OK).toBe(200);
      expect(constants.HTTP_STATUS.UNAUTHORIZED).toBe(401);
    });

    it('should have correct endpoint configurations', () => {
      const constants = IntegrationTestConfig.getTestConstants();
      
      expect(constants.ENDPOINTS.AUTH.LOGIN).toBe('/api/auth/login');
      expect(constants.ENDPOINTS.WEBSITES.BASE).toBe('/api/websites');
      expect(constants.ENDPOINTS.PUBLIC.BLOGS).toBe('/api/public/blogs');
    });
  });
});