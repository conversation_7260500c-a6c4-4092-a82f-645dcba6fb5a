import { TestConfigUtil } from './utils/test-config.util';
import { TestDatabaseUtil } from './utils/test-database.util';

// Setup test environment variables
TestConfigUtil.setupTestEnvironment();

// Global test setup
beforeAll(async () => {
  // Create in-memory database for all tests
  const mongoUri = await TestDatabaseUtil.createMemoryDatabase();
  process.env.MONGODB_URI = mongoUri;
}, 30000);

afterAll(async () => {
  // Close database connection after all tests
  await TestDatabaseUtil.closeDatabase();
}, 30000);

afterEach(async () => {
  // Clear database after each test to ensure test isolation
  await TestDatabaseUtil.clearDatabase();
}, 10000);