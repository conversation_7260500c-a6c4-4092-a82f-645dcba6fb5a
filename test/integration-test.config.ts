import { TestEnvironment } from './utils/test-environment.util';
import { AppModule } from '../src/app.module';

export class IntegrationTestConfig {
  private static testEnvironment: TestEnvironment;

  static async setupIntegrationTest(): Promise<TestEnvironment> {
    if (!this.testEnvironment) {
      this.testEnvironment = new TestEnvironment();
      await this.testEnvironment.setup(AppModule);
    }
    return this.testEnvironment;
  }

  static async teardownIntegrationTest(): Promise<void> {
    if (this.testEnvironment) {
      await this.testEnvironment.teardown();
      this.testEnvironment = null;
    }
  }

  static async clearTestData(): Promise<void> {
    if (this.testEnvironment) {
      await this.testEnvironment.clearDatabase();
    }
  }

  static getTestEnvironment(): TestEnvironment {
    if (!this.testEnvironment) {
      throw new Error('Integration test environment not initialized. Call setupIntegrationTest() first.');
    }
    return this.testEnvironment;
  }

  static getTestConstants() {
    return {
      // Test timeouts
      DEFAULT_TIMEOUT: 30000,
      DATABASE_TIMEOUT: 10000,
      HTTP_TIMEOUT: 5000,

      // Test data constants
      TEST_USER_PASSWORD: 'password123',
      TEST_API_KEY_PREFIX: 'creasoft_',
      TEST_JWT_SECRET: 'test-jwt-secret-key',

      // HTTP status codes for testing
      HTTP_STATUS: {
        OK: 200,
        CREATED: 201,
        BAD_REQUEST: 400,
        UNAUTHORIZED: 401,
        FORBIDDEN: 403,
        NOT_FOUND: 404,
        CONFLICT: 409,
        INTERNAL_SERVER_ERROR: 500,
      },

      // Test endpoints
      ENDPOINTS: {
        AUTH: {
          LOGIN: '/api/auth/login',
          REGISTER: '/api/auth/register',
        },
        WEBSITES: {
          BASE: '/api/websites',
          BY_ID: (id: string) => `/api/websites/${id}`,
        },
        BLOGS: {
          BASE: '/api/blogs',
          BY_ID: (id: string) => `/api/blogs/${id}`,
        },
        PUBLIC: {
          BLOGS: '/api/public/blogs',
          BLOG_BY_SLUG: (slug: string) => `/api/public/blogs/${slug}`,
          CATEGORIES: '/api/public/categories',
          TAGS: '/api/public/tags',
        },
      },
    };
  }
}