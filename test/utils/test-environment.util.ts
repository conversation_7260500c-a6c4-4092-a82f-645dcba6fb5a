import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import * as request from 'supertest';

import { TestDatabaseUtil } from './test-database.util';
import { TestConfigUtil } from './test-config.util';

export class TestEnvironment {
  private app: INestApplication;
  private moduleRef: TestingModule;
  private mongoUri: string;

  async setup(appModule: any): Promise<void> {
    // Setup test environment variables
    TestConfigUtil.setupTestEnvironment();

    // Create in-memory database
    this.mongoUri = await TestDatabaseUtil.createMemoryDatabase();

    // Create testing module
    const moduleBuilder = Test.createTestingModule({
      imports: [
        TestConfigUtil.getTestConfig(),
        MongooseModule.forRoot(this.mongoUri),
        PassportModule.register({ defaultStrategy: 'jwt' }),
        JwtModule.register({
          secret: process.env.JWT_SECRET || 'test-jwt-secret-key',
          signOptions: { expiresIn: '1h' },
        }),
        appModule,
      ],
    });

    this.moduleRef = await moduleBuilder.compile();
    this.app = this.moduleRef.createNestApplication();

    // Apply global configurations
    this.app.setGlobalPrefix('api');

    await this.app.init();
    await this.waitForDatabaseConnection();
  }

  async teardown(): Promise<void> {
    if (this.app) {
      await this.app.close();
    }
    if (this.moduleRef) {
      await this.moduleRef.close();
    }
    await TestDatabaseUtil.closeDatabase();
  }

  async clearDatabase(): Promise<void> {
    await TestDatabaseUtil.clearDatabase();
  }

  getApp(): INestApplication {
    if (!this.app) {
      throw new Error('Test environment not initialized. Call setup() first.');
    }
    return this.app;
  }

  getHttpServer() {
    return this.getApp().getHttpServer();
  }

  request(): request.SuperTest<request.Test> {
    return request(this.getHttpServer());
  }

  getModuleRef(): TestingModule {
    if (!this.moduleRef) {
      throw new Error('Test environment not initialized. Call setup() first.');
    }
    return this.moduleRef;
  }

  async get<T>(token: any): Promise<T> {
    return this.moduleRef.get<T>(token);
  }

  private async waitForDatabaseConnection(): Promise<void> {
    let retries = 20;
    while (retries > 0 && !TestDatabaseUtil.isConnected()) {
      await new Promise(resolve => setTimeout(resolve, 100));
      retries--;
    }
    
    if (!TestDatabaseUtil.isConnected()) {
      throw new Error('Database connection timeout during test setup');
    }
  }
}